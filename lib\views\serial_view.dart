import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/serial_controller.dart';

class SerialView extends StatelessWidget {
  SerialView({super.key});

  final SerialController controller = Get.put(SerialController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(kToolbarHeight),
        child: Obx(
          () => AppBar(
            title: const Text("Arduino Serial Monitor"),
            backgroundColor: controller.isConnected.value
                ? Colors.green
                : Colors.red,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Port selection and connection controls
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Serial Port Connection",
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Obx(
                            () => DropdownButton<String>(
                              value: controller.selectedPort.value,
                              hint: const Text("Select Port"),
                              items: controller.availablePorts.map((
                                String port,
                              ) {
                                return DropdownMenuItem<String>(
                                  value: port,
                                  child: Text(port),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                controller.setSelectedPort(newValue);
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: controller.refreshPorts,
                          child: const Text("Refresh"),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Obx(
                          () => ElevatedButton(
                            onPressed:
                                controller.selectedPort.value != null &&
                                    !controller.isConnected.value
                                ? controller.connectToPort
                                : null,
                            child: const Text("Connect"),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Obx(
                          () => ElevatedButton(
                            onPressed: controller.isConnected.value
                                ? controller.disconnect
                                : null,
                            child: const Text("Disconnect"),
                          ),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: controller.clearReceived,
                          child: const Text("Clear"),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Obx(
                      () => Text(
                        "Status: ${controller.connectionStatus.value}",
                        style: TextStyle(
                          color: controller.isConnected.value
                              ? Colors.green
                              : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Received data display
            Text(
              "Received Data:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Obx(
                    () => Text(
                      controller.received.value.isEmpty
                          ? "No data received yet..."
                          : controller.received.value,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
