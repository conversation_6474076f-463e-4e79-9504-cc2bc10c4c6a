import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart';
import 'package:get/get.dart';

class SerialController extends GetxController {
  // Observable variables
  final RxString received = ''.obs;
  final RxList<String> availablePorts = <String>[].obs;
  final RxnString selectedPort = RxnString();
  final RxBool isConnected = false.obs;
  final RxString connectionStatus = 'Disconnected'.obs;

  // Private variables
  SerialPort? _port;
  SerialPortReader? _reader;

  @override
  void onInit() {
    super.onInit();
    refreshPorts();
  }

  @override
  void onClose() {
    disconnect();
    super.onClose();
  }

  void refreshPorts() {
    availablePorts.value = SerialPort.availablePorts;
    debugPrint("Available ports: ${availablePorts.value}");

    // Try to find Arduino port (usually contains "USB" or "Arduino" in the name)
    if (availablePorts.isNotEmpty) {
      selectedPort.value = availablePorts.firstWhere(
        (port) =>
            port.toLowerCase().contains('usb') ||
            port.toLowerCase().contains('arduino') ||
            port.startsWith('COM'),
        orElse: () => availablePorts.first,
      );
    }
  }

  Future<void> connectToPort() async {
    if (selectedPort.value == null) return;

    try {
      // Close existing connection if any
      await disconnect();

      _port = SerialPort(selectedPort.value!);

      // Configure the port BEFORE opening it
      _port!.config = SerialPortConfig()
        ..baudRate = 9600
        ..bits = 8
        ..parity = SerialPortParity.none
        ..stopBits = 1
        ..setFlowControl(SerialPortFlowControl.none);

      // Open the port
      if (!_port!.openReadWrite()) {
        throw Exception("Failed to open port: ${SerialPort.lastError}");
      }

      // Create reader and start listening
      _reader = SerialPortReader(_port!);
      _reader!.stream.listen(
        (data) {
          // Convert bytes to string and handle it properly
          String newData = utf8.decode(data, allowMalformed: true);
          received.value += newData;
          // Keep only last 1000 characters to prevent memory issues
          if (received.value.length > 1000) {
            received.value = received.value.substring(received.value.length - 1000);
          }
        },
        onError: (error) {
          debugPrint("Reader error: $error");
          connectionStatus.value = "Error: $error";
          isConnected.value = false;
        },
        onDone: () {
          debugPrint("Reader stream closed");
          connectionStatus.value = "Connection closed";
          isConnected.value = false;
        },
      );

      isConnected.value = true;
      connectionStatus.value = "Connected to ${selectedPort.value}";

      debugPrint("Successfully connected to ${selectedPort.value}");
    } catch (e) {
      debugPrint("Connection error: $e");
      connectionStatus.value = "Failed to connect: $e";
      isConnected.value = false;
      await disconnect();
    }
  }

  Future<void> disconnect() async {
    try {
      _reader?.close();
      _reader = null;
      _port?.close();
      _port = null;
      isConnected.value = false;
      connectionStatus.value = "Disconnected";
    } catch (e) {
      debugPrint("Disconnect error: $e");
    }
  }

  void clearReceived() {
    received.value = "";
  }

  void setSelectedPort(String? port) {
    selectedPort.value = port;
  }
}
