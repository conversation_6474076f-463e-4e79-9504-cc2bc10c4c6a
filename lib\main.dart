import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'bindings/serial_binding.dart';
import 'views/serial_view.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Arduino Serial Monitor',
      home: SerialView(),
      initialBinding: SerialBinding(),
    );
  }
}
