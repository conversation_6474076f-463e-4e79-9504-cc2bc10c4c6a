import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: SerialDemo(),
    );
  }
}

class SerialDemo extends StatefulWidget {
  @override
  _SerialDemoState createState() => _SerialDemoState();
}

class _SerialDemoState extends State<SerialDemo> {
  SerialPort? port;
  SerialPortReader? reader;
  String received = "";

  @override
  void initState() {
    super.initState();

    final availablePorts = SerialPort.availablePorts;
    print("Available ports: $availablePorts");

    if (availablePorts.isNotEmpty) {
      port = SerialPort(availablePorts.first);
      if (port!.openReadWrite()) {
        port!.config.baudRate = 115200; // match your STM32 baudrate
        reader = SerialPortReader(port!);
        reader!.stream.listen((Uint8List data) {
          setState(() {
            received += String.fromCharCodes(data);
          });
        });
      } else {
        print("Failed to open port: ${SerialPort.lastError}");
      }
    }
  }

  @override
  void dispose() {
    reader?.close();
    port?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("STM32 Serial Monitor")),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Text(received),
      ),
    );
  }
}
