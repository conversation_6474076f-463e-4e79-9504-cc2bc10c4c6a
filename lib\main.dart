import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_libserialport/flutter_libserialport.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(home: SerialDemo());
  }
}

class SerialDemo extends StatefulWidget {
  const SerialDemo({super.key});

  @override
  State<SerialDemo> createState() => _SerialDemoState();
}

class _SerialDemoState extends State<SerialDemo> {
  SerialPort? port;
  SerialPortReader? reader;
  String received = "";
  List<String> availablePorts = [];
  String? selectedPort;
  bool isConnected = false;
  String connectionStatus = "Disconnected";

  @override
  void initState() {
    super.initState();
    _refreshPorts();
  }

  void _refreshPorts() {
    setState(() {
      availablePorts = SerialPort.availablePorts;
      debugPrint("Available ports: $availablePorts");

      // Try to find Arduino port (usually contains "USB" or "Arduino" in the name)
      if (availablePorts.isNotEmpty) {
        selectedPort = availablePorts.firstWhere(
          (port) =>
              port.toLowerCase().contains('usb') ||
              port.toLowerCase().contains('arduino') ||
              port.startsWith('COM'),
          orElse: () => availablePorts.first,
        );
      }
    });
  }

  void _connectToPort() async {
    if (selectedPort == null) return;

    try {
      // Close existing connection if any
      await _disconnect();

      port = SerialPort(selectedPort!);

      // Configure the port BEFORE opening it
      port!.config = SerialPortConfig()
        ..baudRate = 9600
        ..bits = 8
        ..parity = SerialPortParity.none
        ..stopBits = 1
        ..setFlowControl(SerialPortFlowControl.none);

      // Open the port
      if (!port!.openReadWrite()) {
        throw Exception("Failed to open port: ${SerialPort.lastError}");
      }

      // Create reader and start listening
      reader = SerialPortReader(port!);
      reader!.stream.listen(
        (Uint8List data) {
          // Convert bytes to string and handle it properly
          String newData = utf8.decode(data, allowMalformed: true);
          setState(() {
            received += newData;
            // Keep only last 1000 characters to prevent memory issues
            if (received.length > 1000) {
              received = received.substring(received.length - 1000);
            }
          });
        },
        onError: (error) {
          debugPrint("Reader error: $error");
          setState(() {
            connectionStatus = "Error: $error";
            isConnected = false;
          });
        },
        onDone: () {
          debugPrint("Reader stream closed");
          setState(() {
            connectionStatus = "Connection closed";
            isConnected = false;
          });
        },
      );

      setState(() {
        isConnected = true;
        connectionStatus = "Connected to $selectedPort";
      });

      debugPrint("Successfully connected to $selectedPort");
    } catch (e) {
      debugPrint("Connection error: $e");
      setState(() {
        connectionStatus = "Failed to connect: $e";
        isConnected = false;
      });
      await _disconnect();
    }
  }

  Future<void> _disconnect() async {
    try {
      reader?.close();
      reader = null;
      port?.close();
      port = null;
      setState(() {
        isConnected = false;
        connectionStatus = "Disconnected";
      });
    } catch (e) {
      debugPrint("Disconnect error: $e");
    }
  }

  void _clearReceived() {
    setState(() {
      received = "";
    });
  }

  @override
  void dispose() {
    _disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Arduino Serial Monitor"),
        backgroundColor: isConnected ? Colors.green : Colors.red,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Port selection and connection controls
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Serial Port Connection",
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: DropdownButton<String>(
                            value: selectedPort,
                            hint: Text("Select Port"),
                            items: availablePorts.map((String port) {
                              return DropdownMenuItem<String>(
                                value: port,
                                child: Text(port),
                              );
                            }).toList(),
                            onChanged: (String? newValue) {
                              setState(() {
                                selectedPort = newValue;
                              });
                            },
                          ),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _refreshPorts,
                          child: Text("Refresh"),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: selectedPort != null && !isConnected
                              ? _connectToPort
                              : null,
                          child: Text("Connect"),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: isConnected ? _disconnect : null,
                          child: Text("Disconnect"),
                        ),
                        SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: _clearReceived,
                          child: Text("Clear"),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text(
                      "Status: $connectionStatus",
                      style: TextStyle(
                        color: isConnected ? Colors.green : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),

            // Received data display
            Text(
              "Received Data:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.grey[50],
                ),
                child: SingleChildScrollView(
                  child: Text(
                    received.isEmpty ? "No data received yet..." : received,
                    style: TextStyle(fontFamily: 'monospace', fontSize: 14),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
